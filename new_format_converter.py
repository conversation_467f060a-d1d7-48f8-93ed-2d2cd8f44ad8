#!/usr/bin/env python3
"""
Salla to Shopify Converter for NEW FORMAT
Specifically designed for the new Salla export format with images in [1] القيمة
"""

import csv
import hashlib
import re
import argparse
from typing import Dict, List, Set

def clean_handle(text: str) -> str:
    """Create clean Shopify handle"""
    if not text:
        return 'product'
    
    # Remove Arabic and special characters, keep only alphanumeric and hyphens
    handle = re.sub(r'[^a-zA-Z0-9\s-]', '', text)
    handle = re.sub(r'\s+', '-', handle.strip())
    handle = handle.lower()[:50]
    return handle if handle else 'product'

def generate_short_sku(original_sku: str, color: str = "") -> str:
    """Generate short SKU for Shopify (max 50 characters)"""
    if not original_sku:
        return ""
    
    if len(original_sku) <= 50:
        return original_sku
    
    # For long SKUs (URLs), create hash-based SKU
    if 'http' in original_sku:
        hash_part = hashlib.md5(original_sku.encode()).hexdigest()[:8]
        color_part = f"-{color[:5]}" if color else ""
        return f"SKU-{hash_part}{color_part}"[:50]
    else:
        # Regular SKU, truncate
        base = original_sku[:40]
        color_part = f"-{color[:5]}" if color else ""
        return f"{base}{color_part}"[:50]

def is_valid_image_url(url: str) -> bool:
    """Check if URL is a valid image URL"""
    if not url or not isinstance(url, str):
        return False
    url = url.strip()
    if not url:
        return False
    # Check if it's a proper URL and not a color code or text
    return (url.startswith('http://') or url.startswith('https://')) and \
           ('.jpg' in url or '.png' in url or '.jpeg' in url) and \
           not any(invalid in url for invalid in ['#', 'Black', 'Pink', 'Gold', 'Silver', 'Green', 'Blue'])

def convert_new_format_to_shopify(input_file: str, output_file: str):
    """Convert new format Salla CSV to Shopify CSV"""
    
    print(f"Loading new format Salla CSV from: {input_file}")
    
    # Read the CSV file
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        rows = list(reader)
    
    print(f"Loaded {len(rows)} rows")
    
    # Separate main products and variants
    main_products = [row for row in rows if row.get('MPN', '').strip() == 'منتج']
    variants = [row for row in rows if row.get('MPN', '').strip() == 'خيار']
    
    print(f"Found {len(main_products)} main products and {len(variants)} variants")
    
    # Group variants by product name (since product ID is empty)
    products_by_name = {}
    
    # First, add main products
    for main_product in main_products:
        product_name = main_product.get('الباركود', '').strip()  # Product name is in الباركود field
        if product_name:
            if product_name not in products_by_name:
                products_by_name[product_name] = {'main': None, 'variants': []}
            products_by_name[product_name]['main'] = main_product

    # Then, add variants to their corresponding products
    for variant in variants:
        product_name = variant.get('الباركود', '').strip()  # Product name is in الباركود field
        if product_name:
            if product_name not in products_by_name:
                products_by_name[product_name] = {'main': None, 'variants': []}
            products_by_name[product_name]['variants'].append(variant)
    
    print(f"Grouped into {len(products_by_name)} unique products")
    
    # Shopify CSV headers
    shopify_headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Category', 'Type', 
        'Tags', 'Published', 'Option1 Name', 'Option1 Value', 'Option2 Name', 
        'Option2 Value', 'Option3 Name', 'Option3 Value', 'Variant SKU', 
        'Variant Grams', 'Variant Inventory Tracker', 'Variant Inventory Qty', 
        'Variant Inventory Policy', 'Variant Fulfillment Service', 'Variant Price', 
        'Variant Compare At Price', 'Variant Requires Shipping', 'Variant Taxable', 
        'Variant Barcode', 'Image Src', 'Image Position', 'Image Alt Text', 
        'Gift Card', 'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category', 
        'Google Shopping / Gender', 'Google Shopping / Age Group', 'Google Shopping / MPN', 
        'Google Shopping / AdWords Grouping', 'Google Shopping / AdWords Labels', 
        'Google Shopping / Condition', 'Google Shopping / Custom Product', 
        'Google Shopping / Custom Label 0', 'Google Shopping / Custom Label 1', 
        'Google Shopping / Custom Label 2', 'Google Shopping / Custom Label 3', 
        'Google Shopping / Custom Label 4', 'Variant Image', 'Variant Weight Unit', 
        'Variant Tax Code', 'Cost per item', 'Included / Saudi Arabia', 'Price / Saudi Arabia', 
        'Compare At Price / Saudi Arabia', 'Status'
    ]
    
    shopify_rows = []
    products_with_images = 0
    products_without_images = 0
    
    # Process each product
    for product_name, product_data in products_by_name.items():
        main_product = product_data['main']
        variants = product_data['variants']
        
        if not main_product:
            continue
        
        # Extract product info from main product
        handle = clean_handle(product_name)
        description = main_product.get('الكمية', '').strip()      # Description is here!
        category = main_product.get('سعر التخفيض', '').strip()    # Category is here!
        price = main_product.get('السعر', '').strip()
        sku = main_product.get('رمز المنتج sku', '').strip()
        
        # Extract images from main product (comma-separated in سعر التكلفة)
        main_images = []
        product_images = main_product.get('سعر التكلفة', '').strip()  # Images are here!
        if product_images:
            for img in product_images.split(','):
                img = img.strip()
                if is_valid_image_url(img):
                    main_images.append(img)

        # Extract images from variants
        variant_images = {}
        for variant in variants:
            color = variant.get('[1] القيمة', '').strip()      # Color name is here!
            image_url = variant.get('[1] النوع', '').strip()   # Image URL is here!

            if color and is_valid_image_url(image_url):
                variant_images[color] = image_url
        
        # Use main images if available, otherwise use variant images
        all_images = main_images if main_images else list(variant_images.values())
        
        # Create main product row
        main_row = {
            'Handle': handle,
            'Title': product_name,
            'Body (HTML)': description,
            'Vendor': 'ChrisBella',
            'Product Category': category,
            'Type': 'Handbag',
            'Published': 'TRUE',
            'Variant Price': price,
            'Variant SKU': generate_short_sku(sku, ''),
            'Variant Inventory Tracker': 'shopify',
            'Variant Inventory Qty': '10',
            'Variant Inventory Policy': 'deny',
            'Variant Fulfillment Service': 'manual',
            'Variant Requires Shipping': 'TRUE',
            'Variant Taxable': 'TRUE',
            'Variant Barcode': '',
            'Image Src': all_images[0] if all_images else '',
            'Image Position': '1',
            'Gift Card': 'FALSE',
            'Status': 'active'
        }
        
        # Fill remaining fields
        for header in shopify_headers:
            if header not in main_row:
                main_row[header] = ''
        
        shopify_rows.append(main_row)
        
        if all_images:
            products_with_images += 1
        else:
            products_without_images += 1
        
        # Add variant rows for each color
        seen_colors = set()
        for color, image_url in variant_images.items():
            if color and color not in seen_colors:
                seen_colors.add(color)
                
                variant_row = {
                    'Handle': handle,
                    'Option1 Name': 'Color',
                    'Option1 Value': color,
                    'Variant Price': price,
                    'Variant SKU': generate_short_sku(sku, color),
                    'Variant Inventory Tracker': 'shopify',
                    'Variant Inventory Qty': '10',
                    'Variant Inventory Policy': 'deny',
                    'Variant Fulfillment Service': 'manual',
                    'Variant Requires Shipping': 'TRUE',
                    'Variant Taxable': 'TRUE',
                    'Variant Barcode': '',
                    'Variant Image': image_url,
                    'Status': 'active'
                }
                
                # Fill remaining fields
                for header in shopify_headers:
                    if header not in variant_row:
                        variant_row[header] = ''
                
                shopify_rows.append(variant_row)
                
                if len(seen_colors) >= 99:  # Shopify limit
                    break
        
        # Add additional images as separate rows
        for i, image_url in enumerate(all_images[1:6], 2):  # Max 5 additional images
            image_row = {
                'Handle': handle,
                'Image Src': image_url,
                'Image Position': str(i),
                'Status': 'active'
            }
            
            # Fill remaining fields
            for header in shopify_headers:
                if header not in image_row:
                    image_row[header] = ''
            
            shopify_rows.append(image_row)
    
    # Write the Shopify CSV
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=shopify_headers)
        writer.writeheader()
        writer.writerows(shopify_rows)
    
    print(f"\n=== CONVERSION COMPLETE ===")
    print(f"Generated {len(shopify_rows)} Shopify rows")
    print(f"Products with images: {products_with_images}")
    print(f"Products without images: {products_without_images}")
    print(f"Output file: {output_file}")
    print(f"Ready for Shopify import!")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Convert new format Salla CSV to Shopify CSV')
    parser.add_argument('input_file', help='Input Salla CSV file')
    parser.add_argument('-o', '--output', default='shopify_products_new_format.csv', help='Output Shopify CSV file')
    
    args = parser.parse_args()
    
    convert_new_format_to_shopify(args.input_file, args.output)
