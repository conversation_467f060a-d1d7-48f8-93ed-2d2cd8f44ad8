#!/usr/bin/env python3
"""
Fix Shopify-specific field values for successful import
"""

import csv

def fix_shopify_fields(input_file, output_file):
    """Fix Shopify field values that are causing import errors"""
    print(f"Fixing Shopify field values in {input_file}...")
    
    rows_fixed = 0
    total_rows = 0
    
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        fieldnames = reader.fieldnames
        
        rows = []
        for row in reader:
            total_rows += 1
            
            # Fix Variant Fulfillment Service
            fulfillment = row.get('Variant Fulfillment Service', '').strip()
            if fulfillment in ['manual', ''] or not fulfillment:
                row['Variant Fulfillment Service'] = 'manual'
                if fulfillment != 'manual':
                    rows_fixed += 1
                    print(f"Fixed fulfillment service in row {total_rows}: '{fulfillment}' -> 'manual'")
            
            # Fix Variant Inventory Policy
            inventory_policy = row.get('Variant Inventory Policy', '').strip()
            if inventory_policy in ['deny', ''] or not inventory_policy:
                row['Variant Inventory Policy'] = 'deny'
                if inventory_policy != 'deny':
                    rows_fixed += 1
                    print(f"Fixed inventory policy in row {total_rows}: '{inventory_policy}' -> 'deny'")
            
            # Fix Variant Inventory Tracker
            inventory_tracker = row.get('Variant Inventory Tracker', '').strip()
            if inventory_tracker in ['shopify', ''] or not inventory_tracker:
                row['Variant Inventory Tracker'] = 'shopify'
                if inventory_tracker != 'shopify':
                    rows_fixed += 1
                    print(f"Fixed inventory tracker in row {total_rows}: '{inventory_tracker}' -> 'shopify'")
            
            # Ensure Published is TRUE or FALSE (not empty)
            published = row.get('Published', '').strip()
            if published not in ['TRUE', 'FALSE']:
                row['Published'] = 'TRUE'
                rows_fixed += 1
                print(f"Fixed published status in row {total_rows}: '{published}' -> 'TRUE'")
            
            # Ensure Gift Card is FALSE (not empty)
            gift_card = row.get('Gift Card', '').strip()
            if gift_card not in ['TRUE', 'FALSE']:
                row['Gift Card'] = 'FALSE'
                if gift_card != 'FALSE':
                    rows_fixed += 1
                    print(f"Fixed gift card in row {total_rows}: '{gift_card}' -> 'FALSE'")
            
            # Ensure Variant Requires Shipping is TRUE or FALSE
            requires_shipping = row.get('Variant Requires Shipping', '').strip()
            if requires_shipping not in ['TRUE', 'FALSE']:
                row['Variant Requires Shipping'] = 'TRUE'
                rows_fixed += 1
                print(f"Fixed requires shipping in row {total_rows}: '{requires_shipping}' -> 'TRUE'")
            
            # Ensure Variant Taxable is TRUE or FALSE
            taxable = row.get('Variant Taxable', '').strip()
            if taxable not in ['TRUE', 'FALSE']:
                row['Variant Taxable'] = 'TRUE'
                rows_fixed += 1
                print(f"Fixed taxable in row {total_rows}: '{taxable}' -> 'TRUE'")
            
            rows.append(row)
    
    # Write fixed CSV
    with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(rows)
    
    print(f"\n✅ Fixed {rows_fixed} field issues out of {total_rows} total rows")
    print(f"📁 Fixed file saved as: {output_file}")

if __name__ == "__main__":
    fix_shopify_fields("shopify-import-corrected.csv", "shopify-import-ready.csv")
