#!/usr/bin/env python3
"""
Fix invalid prices in Shopify CSV
"""

import csv
import re

def fix_prices(input_file, output_file):
    """Fix invalid price values in CSV"""
    print(f"Fixing prices in {input_file}...")
    
    rows_fixed = 0
    total_rows = 0
    
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        fieldnames = reader.fieldnames
        
        rows = []
        for row in reader:
            total_rows += 1
            
            # Fix Variant Price
            price = row.get('Variant Price', '').strip()
            if price in ['نعم', 'Yes', 'TRUE', 'FALSE', 'لا', 'No', ''] or not price:
                row['Variant Price'] = '219.00'  # Default price
                rows_fixed += 1
                print(f"Fixed price in row {total_rows}: '{price}' -> '219.00'")
            else:
                # Validate it's a number
                try:
                    float(price)
                except (ValueError, TypeError):
                    row['Variant Price'] = '219.00'
                    rows_fixed += 1
                    print(f"Fixed invalid price in row {total_rows}: '{price}' -> '219.00'")
            
            # Fix Variant Compare At Price if needed
            compare_price = row.get('Variant Compare At Price', '').strip()
            if compare_price in ['نعم', 'Yes', 'TRUE', 'FALSE', 'لا', 'No']:
                row['Variant Compare At Price'] = ''  # Empty for compare price
                print(f"Cleared invalid compare price in row {total_rows}: '{compare_price}' -> ''")
            
            rows.append(row)
    
    # Write fixed CSV
    with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(rows)
    
    print(f"\n✅ Fixed {rows_fixed} price issues out of {total_rows} total rows")
    print(f"📁 Fixed file saved as: {output_file}")

if __name__ == "__main__":
    fix_prices("shopify-import.csv", "shopify-import-corrected.csv")
