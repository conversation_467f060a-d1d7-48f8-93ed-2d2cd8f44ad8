#!/usr/bin/env python3
"""
Image URL Validator for Shopify Import
Validates all image URLs in the converted CSV file
"""

import csv
import requests
from urllib.parse import urlparse
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

def validate_url(url):
    """Validate a single image URL"""
    if not url or not url.strip():
        return {'url': url, 'status': 'empty', 'accessible': False}
    
    url = url.strip()
    
    # Check if it's a valid URL format
    try:
        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            return {'url': url, 'status': 'invalid_format', 'accessible': False}
    except Exception as e:
        return {'url': url, 'status': f'parse_error: {e}', 'accessible': False}
    
    # Try to access the URL
    try:
        response = requests.head(url, timeout=10, allow_redirects=True)
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            if 'image' in content_type:
                return {'url': url, 'status': 'valid_image', 'accessible': True, 'content_type': content_type}
            else:
                return {'url': url, 'status': f'not_image: {content_type}', 'accessible': True}
        else:
            return {'url': url, 'status': f'http_error: {response.status_code}', 'accessible': False}
    except requests.exceptions.Timeout:
        return {'url': url, 'status': 'timeout', 'accessible': False}
    except requests.exceptions.RequestException as e:
        return {'url': url, 'status': f'request_error: {e}', 'accessible': False}
    except Exception as e:
        return {'url': url, 'status': f'unknown_error: {e}', 'accessible': False}

def extract_image_urls(csv_file):
    """Extract all image URLs from the CSV file"""
    image_urls = set()
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            # Main product images
            if row.get('Image Src'):
                image_urls.add(row['Image Src'].strip())
            
            # Variant images
            if row.get('Variant Image'):
                image_urls.add(row['Variant Image'].strip())
    
    return list(image_urls)

def validate_images_batch(urls, max_workers=10):
    """Validate multiple URLs concurrently"""
    results = []
    
    print(f"Validating {len(urls)} unique image URLs...")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_url = {executor.submit(validate_url, url): url for url in urls}
        
        # Process completed tasks
        for i, future in enumerate(as_completed(future_to_url), 1):
            result = future.result()
            results.append(result)
            
            # Progress indicator
            if i % 50 == 0 or i == len(urls):
                print(f"Progress: {i}/{len(urls)} URLs validated")
    
    return results

def generate_report(results, output_file):
    """Generate validation report"""
    total_urls = len(results)
    accessible_urls = sum(1 for r in results if r['accessible'])
    valid_images = sum(1 for r in results if r['status'] == 'valid_image')
    
    # Group by status
    status_counts = {}
    for result in results:
        status = result['status']
        status_counts[status] = status_counts.get(status, 0) + 1
    
    # Failed URLs
    failed_urls = [r for r in results if not r['accessible']]
    
    report = {
        'summary': {
            'total_urls': total_urls,
            'accessible_urls': accessible_urls,
            'valid_images': valid_images,
            'success_rate': f"{(accessible_urls/total_urls*100):.1f}%" if total_urls > 0 else "0%",
            'image_success_rate': f"{(valid_images/total_urls*100):.1f}%" if total_urls > 0 else "0%"
        },
        'status_breakdown': status_counts,
        'failed_urls': failed_urls[:20],  # Show first 20 failed URLs
        'total_failed': len(failed_urls)
    }
    
    # Save detailed report
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    return report

def print_summary(report):
    """Print validation summary"""
    print("\n" + "="*60)
    print("IMAGE VALIDATION SUMMARY")
    print("="*60)
    
    summary = report['summary']
    print(f"Total URLs: {summary['total_urls']}")
    print(f"Accessible URLs: {summary['accessible_urls']}")
    print(f"Valid Images: {summary['valid_images']}")
    print(f"Success Rate: {summary['success_rate']}")
    print(f"Image Success Rate: {summary['image_success_rate']}")
    
    print("\nStatus Breakdown:")
    for status, count in report['status_breakdown'].items():
        print(f"  {status}: {count}")
    
    if report['total_failed'] > 0:
        print(f"\nFailed URLs: {report['total_failed']}")
        print("First few failed URLs:")
        for failed in report['failed_urls'][:5]:
            print(f"  {failed['url']} - {failed['status']}")
    
    print("\n" + "="*60)

def main():
    csv_file = "shopify-import.csv"
    report_file = "image_validation_report.json"
    
    try:
        # Extract image URLs
        print("Extracting image URLs from CSV...")
        urls = extract_image_urls(csv_file)
        print(f"Found {len(urls)} unique image URLs")
        
        if not urls:
            print("No image URLs found in the CSV file!")
            return
        
        # Validate URLs
        results = validate_images_batch(urls)
        
        # Generate report
        print("\nGenerating validation report...")
        report = generate_report(results, report_file)
        
        # Print summary
        print_summary(report)
        
        print(f"\n✅ Image validation completed!")
        print(f"📊 Detailed report saved to: {report_file}")
        
        # Recommendations
        success_rate = float(report['summary']['success_rate'].rstrip('%'))
        if success_rate >= 95:
            print("🎉 Excellent! Almost all images are accessible.")
        elif success_rate >= 85:
            print("✅ Good! Most images are accessible.")
        elif success_rate >= 70:
            print("⚠️  Warning: Some images may not load properly.")
        else:
            print("❌ Alert: Many images are not accessible. Review failed URLs.")
        
    except Exception as e:
        print(f"❌ Error during image validation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
