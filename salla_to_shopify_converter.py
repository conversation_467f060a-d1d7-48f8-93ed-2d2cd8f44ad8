#!/usr/bin/env python3
"""
Salla to Shopify CSV Converter
Converts Salla export format to Shopify import format
"""

import csv
import re
import html
from urllib.parse import urlparse
import unicodedata

def clean_html(html_content):
    """Clean and simplify HTML content for Shopify"""
    if not html_content:
        return ""
    
    # Remove complex styling and keep basic formatting
    html_content = re.sub(r'style="[^"]*"', '', html_content)
    html_content = re.sub(r'class="[^"]*"', '', html_content)
    
    # Convert RTL direction tags to simple paragraphs
    html_content = re.sub(r'<p class="ql-direction-rtl">', '<p>', html_content)
    
    # Clean up extra spaces and line breaks
    html_content = re.sub(r'\s+', ' ', html_content)
    html_content = re.sub(r'<p><br></p>', '', html_content)
    html_content = re.sub(r'<p>\s*</p>', '', html_content)
    
    return html_content.strip()

def create_handle(title, sku):
    """Create a unique Shopify handle from title and SKU"""
    # Create English-based handle from product type and SKU
    if 'حقيبة سهرة' in title:
        base_handle = 'evening-bag'
    elif 'حقيبة كتف' in title:
        base_handle = 'shoulder-bag'
    elif 'حقيبة يد' in title:
        base_handle = 'handbag'
    elif 'حقيبة كروس' in title or 'كروس بودي' in title:
        base_handle = 'crossbody-bag'
    elif 'حقيبة باوتش' in title:
        base_handle = 'pouch-bag'
    elif 'حقيبة توتي' in title:
        base_handle = 'tote-bag'
    else:
        base_handle = 'bag'

    # Add SKU for uniqueness
    if sku:
        sku_clean = re.sub(r'[^\w]', '', str(sku))
        handle = f"{base_handle}-{sku_clean}"
    else:
        handle = base_handle

    # Ensure ASCII only and proper format
    handle = re.sub(r'[^a-z0-9-]', '', handle.lower())
    handle = re.sub(r'-+', '-', handle)  # Remove multiple hyphens
    handle = handle.strip('-')

    return handle[:100]  # Shopify handle limit

def extract_base_sku(variant_sku):
    """Extract base SKU pattern from variant SKU"""
    if not variant_sku:
        return ""

    sku_str = str(variant_sku).strip()

    # Handle different SKU patterns:
    # 1. Numbers only (e.g., 32210001) - use as is
    # 2. Numbers + color (e.g., 32210001black) - remove color
    # 3. Complex patterns (e.g., 12502018-off white) - use everything before color
    # 4. Letter products (e.g., 506225779121 for letters) - use as is

    # If it's purely numeric, use as is
    if sku_str.isdigit():
        return sku_str

    # Remove common color suffixes but preserve the base pattern
    # First try to remove color words at the end
    base_sku = re.sub(r'(black|white|pink|gold|silver|brown|green|blue|red|navy|beige|cream|coffee|gray|grey|wine|army|khaki|champagne|gun|off|dark|light)$', '', sku_str, flags=re.IGNORECASE)
    base_sku = base_sku.rstrip(' -_')

    # If nothing was removed, try other patterns
    if base_sku == sku_str:
        # Remove trailing letters after numbers
        base_sku = re.sub(r'([0-9]+)[a-zA-Z\s]+$', r'\1', sku_str)

    # If still nothing, use the original
    if not base_sku or base_sku == sku_str:
        base_sku = sku_str

    return base_sku

def split_images(image_string):
    """Split comma-separated image URLs"""
    if not image_string:
        return []
    
    images = [img.strip() for img in image_string.split(',') if img.strip()]
    return images

def generate_tags(category, title):
    """Generate tags from category and title"""
    tags = []
    
    if category:
        tags.append(category)
    
    # Add common bag-related tags
    if 'حقيبة' in title:
        tags.append('حقائب')
    if 'سهرة' in title:
        tags.append('سهرات')
    if 'كتف' in title:
        tags.append('حقائب كتف')
    if 'يد' in title:
        tags.append('حقائب يد')
    
    return ', '.join(tags)

def process_salla_data(input_file, output_file):
    """Main processing function"""
    products = {}  # Group products by base SKU or title

    print("Reading Salla CSV data...")

    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)

        for row in reader:
            mpn = row.get('MPN', '').strip()
            variant_sku = row.get('Variant SKU', '').strip()
            title = row.get('Title', '').strip()

            # Skip empty rows
            if not title and not variant_sku:
                continue

            # Create a unique product key
            if variant_sku:
                base_sku = extract_base_sku(variant_sku)
                product_key = f"{title}_{base_sku}" if base_sku else title
            else:
                product_key = title

            if not product_key:
                continue

            if product_key not in products:
                products[product_key] = {
                    'main_product': None,
                    'variants': [],
                    'title': title
                }

            if mpn == 'منتج':
                # This is a main product row
                products[product_key]['main_product'] = row
            elif mpn == 'خيار':
                # This is a variant row
                products[product_key]['variants'].append(row)
            else:
                # Handle rows without clear MPN - treat as variants if they have SKU
                if variant_sku:
                    products[product_key]['variants'].append(row)
    
    print(f"Found {len(products)} product groups")
    
    # Generate Shopify CSV
    print("Generating Shopify CSV...")
    
    shopify_rows = []

    for product_key, product_data in products.items():
        main_product = product_data['main_product']
        variants = product_data['variants']
        title = product_data['title']

        # If no main product but we have variants, create a basic main product from first variant
        if not main_product and variants:
            main_product = variants[0].copy()
            main_product['Body (HTML)'] = f"<p>{title}</p>"  # Basic description
            main_product['Product Category'] = main_product.get('Product Category', 'منتجات')
            print(f"Created main product from variant for: {title}")

        if not main_product or not variants:
            print(f"Warning: No data found for product: {title}")
            continue
        
        # Extract main product data
        title = main_product.get('Title', '').strip()
        category = main_product.get('Product Category', '').strip()
        body_html = clean_html(main_product.get('Body (HTML)', ''))
        main_images = split_images(main_product.get('Image Src', ''))

        # Clean and validate price
        raw_price = main_product.get('Variant Price', '219.00')
        if raw_price in ['نعم', 'Yes', 'TRUE', 'FALSE', 'لا', 'No'] or not raw_price:
            price = '219.00'  # Default price for invalid values
        else:
            try:
                # Try to convert to float to validate
                float(raw_price)
                price = raw_price
            except (ValueError, TypeError):
                price = '219.00'  # Default price for invalid values
        
        # Create product handle
        handle = create_handle(title, base_sku)
        
        # Generate tags
        tags = generate_tags(category, title)
        
        # Create main product row (first variant)
        first_variant = variants[0] if variants else {}

        # Clean first variant price
        first_variant_raw_price = first_variant.get('Variant Price', price)
        if first_variant_raw_price in ['نعم', 'Yes', 'TRUE', 'FALSE', 'لا', 'No'] or not first_variant_raw_price:
            first_variant_price = price  # Use main product price
        else:
            try:
                float(first_variant_raw_price)
                first_variant_price = first_variant_raw_price
            except (ValueError, TypeError):
                first_variant_price = price  # Use main product price

        main_row = {
            'Handle': handle,
            'Title': title,
            'Body (HTML)': body_html,
            'Vendor': 'ChrisBella',
            'Product Type': category or 'حقائب',
            'Tags': tags,
            'Published': 'TRUE',
            'Option1 Name': 'Color',
            'Option1 Value': first_variant.get('Option1 Value', ''),
            'Option2 Name': '',
            'Option2 Value': '',
            'Option3 Name': '',
            'Option3 Value': '',
            'Variant SKU': first_variant.get('Variant SKU', ''),
            'Variant Grams': '500',  # Default weight
            'Variant Inventory Tracker': 'shopify',
            'Variant Inventory Qty': '10',  # Default inventory
            'Variant Inventory Policy': 'deny',
            'Variant Fulfillment Service': 'manual',
            'Variant Price': first_variant_price,
            'Variant Compare At Price': '',
            'Variant Requires Shipping': 'TRUE',
            'Variant Taxable': 'TRUE',
            'Variant Barcode': '',
            'Image Src': main_images[0] if main_images else '',
            'Image Position': '1',
            'Image Alt Text': title,
            'Gift Card': 'FALSE',
            'SEO Title': title,
            'SEO Description': f"{title} - {category}" if category else title,
            'Google Shopping / Google Product Category': '',
            'Google Shopping / MPN': '',
            'Google Shopping / Age Group': 'adult',
            'Google Shopping / Gender': 'female',
            'Google Shopping / Custom Product': 'FALSE',
            'Google Shopping / Custom Label 0': '',
            'Google Shopping / Custom Label 1': '',
            'Google Shopping / Custom Label 2': '',
            'Google Shopping / Custom Label 3': '',
            'Google Shopping / Custom Label 4': '',
            'Variant Image': first_variant.get('Variant Image', ''),
            'Variant Weight Unit': 'g',
            'Status': 'active'
        }
        
        shopify_rows.append(main_row)
        
        # Add additional main product images
        for i, image_url in enumerate(main_images[1:], 2):
            image_row = {key: '' for key in main_row.keys()}
            image_row.update({
                'Handle': handle,
                'Image Src': image_url,
                'Image Position': str(i),
                'Image Alt Text': title
            })
            shopify_rows.append(image_row)
        
        # Add remaining variants
        for variant in variants[1:]:
            # Clean variant price
            variant_raw_price = variant.get('Variant Price', price)
            if variant_raw_price in ['نعم', 'Yes', 'TRUE', 'FALSE', 'لا', 'No'] or not variant_raw_price:
                variant_price = price  # Use main product price
            else:
                try:
                    float(variant_raw_price)
                    variant_price = variant_raw_price
                except (ValueError, TypeError):
                    variant_price = price  # Use main product price

            variant_row = {key: '' for key in main_row.keys()}
            variant_row.update({
                'Handle': handle,
                'Option1 Name': 'Color',
                'Option1 Value': variant.get('Option1 Value', ''),
                'Variant SKU': variant.get('Variant SKU', ''),
                'Variant Grams': '500',
                'Variant Inventory Tracker': 'shopify',
                'Variant Inventory Qty': '10',
                'Variant Inventory Policy': 'deny',
                'Variant Fulfillment Service': 'manual',
                'Variant Price': variant_price,
                'Variant Compare At Price': '',
                'Variant Requires Shipping': 'TRUE',
                'Variant Taxable': 'TRUE',
                'Variant Barcode': '',
                'Variant Image': variant.get('Variant Image', ''),
                'Variant Weight Unit': 'g'
            })
            shopify_rows.append(variant_row)
    
    # Write Shopify CSV
    if shopify_rows:
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=shopify_rows[0].keys())
            writer.writeheader()
            writer.writerows(shopify_rows)
        
        print(f"Successfully created {output_file} with {len(shopify_rows)} rows")
        print(f"Products: {len(products)}")
        print(f"Total variants: {sum(len(p['variants']) for p in products.values())}")
    else:
        print("No data to write!")

if __name__ == "__main__":
    input_file = "cb-sfy.csv"
    output_file = "shopify-import.csv"
    
    try:
        process_salla_data(input_file, output_file)
        print("\n✅ Conversion completed successfully!")
        print(f"📁 Output file: {output_file}")
        print("🚀 Ready for Shopify import!")
        
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        import traceback
        traceback.print_exc()
