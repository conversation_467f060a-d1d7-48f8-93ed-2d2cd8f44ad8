#!/usr/bin/env python3
"""
Create proper Shopify CSV with variants and real images
"""

import csv
from collections import defaultdict

def create_proper_shopify_csv(input_file, output_file):
    """Create proper Shopify CSV with variants"""
    print(f"Creating proper Shopify CSV from {input_file}...")
    
    # Shopify headers
    shopify_headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Type', 'Tags',
        'Published', 'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value',
        'Option3 Name', 'Option3 Value', 'Variant SKU', 'Variant Grams',
        'Variant Inventory Tracker', 'Variant Inventory Qty', 'Variant Inventory Policy',
        'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode',
        'Image Src', 'Image Position', 'Image Alt Text', 'Gift Card',
        'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / MPN', 'Google Shopping / Age Group', 'Google Shopping / Gender',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0',
        'Google Shopping / Custom Label 1', 'Google Shopping / Custom Label 2',
        'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]
    
    # Group products by name
    products = defaultdict(list)
    
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            product_name = row.get('سعر التخفيض', '').strip()  # This contains the product name
            if product_name:
                products[product_name].append(row)
    
    print(f"Found {len(products)} unique products")
    
    rows_to_write = []
    
    for product_name, product_rows in products.items():
        # Skip if not a real product name
        if not product_name or product_name.replace('.', '').isdigit():
            continue
            
        print(f"Processing: {product_name}")
        
        # Separate main product row and variant rows
        main_product_row = None
        variant_rows = []
        
        for row in product_rows:
            mpn = row.get('MPN', '').strip()
            if mpn == 'منتج':  # Main product
                main_product_row = row
            elif mpn == 'خيار':  # Variant
                variant_rows.append(row)
        
        if not main_product_row and variant_rows:
            # Use first variant as main product
            main_product_row = variant_rows[0]
            variant_rows = variant_rows[1:]
        
        if not main_product_row:
            continue
        
        # Create handle
        if 'حروف بني' in product_name:
            handle = 'brown-letters'
        elif 'حروف' in product_name:
            handle = 'letters'
        elif 'حقيبة سهرة' in product_name:
            handle = 'evening-bag'
        elif 'حقيبة' in product_name:
            handle = 'handbag'
        else:
            handle = 'product'
        
        # Add unique ID
        product_id = main_product_row.get('GTIN', '')[:8]
        handle = f"{handle}-{product_id}"
        
        # Determine price
        price = '39.00' if 'حروف' in product_name else '219.00'
        
        # Get main product images
        main_images = []
        main_image_field = main_product_row.get('صورة المنتج', '').strip()
        if main_image_field:
            main_images = [img.strip() for img in main_image_field.split(',') if img.strip().startswith('http')]
        
        # Create main product row
        main_row = {
            'Handle': handle,
            'Title': product_name,
            'Body (HTML)': f'<p>{product_name}</p>',
            'Vendor': 'ChrisBella',
            'Product Type': 'حروف' if 'حروف' in product_name else 'حقائب',
            'Tags': f'{product_name}, حروف' if 'حروف' in product_name else f'{product_name}, حقائب',
            'Published': 'TRUE',
            'Option1 Name': 'Letter' if 'حروف' in product_name else 'Color',
            'Option1 Value': variant_rows[0].get('[2] القيمة', 'Default') if variant_rows else 'Default',
            'Variant SKU': variant_rows[0].get('رمز المنتج sku', product_id) if variant_rows else product_id,
            'Variant Grams': '500',
            'Variant Inventory Tracker': 'shopify',
            'Variant Inventory Qty': '10',
            'Variant Inventory Policy': 'deny',
            'Variant Fulfillment Service': 'manual',
            'Variant Price': price,
            'Variant Requires Shipping': 'TRUE',
            'Variant Taxable': 'TRUE',
            'Image Src': main_images[0] if main_images else '',
            'Image Position': '1',
            'Image Alt Text': product_name,
            'Gift Card': 'FALSE',
            'SEO Title': product_name,
            'SEO Description': product_name,
            'Google Shopping / Age Group': 'adult',
            'Google Shopping / Gender': 'female',
            'Google Shopping / Custom Product': 'FALSE',
            'Variant Image': variant_rows[0].get('[2] الصورة / اللون', '') if variant_rows else '',
            'Variant Weight Unit': 'g',
            'Status': 'active'
        }
        
        rows_to_write.append(main_row)
        
        # Add additional main product images
        for i, img_url in enumerate(main_images[1:], 2):
            if img_url.startswith('http'):
                image_row = {key: '' for key in shopify_headers}
                image_row.update({
                    'Handle': handle,
                    'Image Src': img_url,
                    'Image Position': str(i),
                    'Image Alt Text': product_name,
                    'Published': 'TRUE',
                    'Gift Card': 'FALSE'
                })
                rows_to_write.append(image_row)
        
        # Add variant rows (skip first one as it's already in main row)
        for variant_row in variant_rows[1:]:
            variant_name = variant_row.get('[2] القيمة', '').strip()
            variant_sku = variant_row.get('رمز المنتج sku', '').strip()
            variant_image = variant_row.get('[2] الصورة / اللون', '').strip()
            
            if variant_name and variant_sku:
                variant_shopify_row = {key: '' for key in shopify_headers}
                variant_shopify_row.update({
                    'Handle': handle,
                    'Option1 Name': 'Letter' if 'حروف' in product_name else 'Color',
                    'Option1 Value': variant_name,
                    'Variant SKU': variant_sku,
                    'Variant Grams': '500',
                    'Variant Inventory Tracker': 'shopify',
                    'Variant Inventory Qty': '10',
                    'Variant Inventory Policy': 'deny',
                    'Variant Fulfillment Service': 'manual',
                    'Variant Price': price,
                    'Variant Requires Shipping': 'TRUE',
                    'Variant Taxable': 'TRUE',
                    'Variant Image': variant_image if variant_image.startswith('http') else '',
                    'Variant Weight Unit': 'g',
                    'Published': 'TRUE',
                    'Gift Card': 'FALSE',
                    'Status': 'active'
                })
                rows_to_write.append(variant_shopify_row)
    
    # Write CSV
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=shopify_headers)
        writer.writeheader()
        writer.writerows(rows_to_write)
    
    print(f"✅ Created Shopify CSV: {len(rows_to_write)} rows")
    print(f"📁 Output: {output_file}")

if __name__ == "__main__":
    create_proper_shopify_csv(
        "chrisbella_24-08-2025-12-00_jCmuZwaX8DClwrXDKhYVyL1rSD1f62pl9wUURec4_products.csv",
        "shopify-import-clean.csv"
    )
