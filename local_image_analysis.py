#!/usr/bin/env python3
"""
Comprehensive Local Image Analysis
Focus on the most important aspect - IMAGES
"""

import csv
import json
from collections import defaultdict

def analyze_images_locally():
    """Complete local analysis focusing on images"""
    
    print("🔍 COMPREHENSIVE LOCAL IMAGE ANALYSIS")
    print("=" * 50)
    
    # Read the Salla file locally
    file_path = '/Users/<USER>/Desktop/Cs/chrisbella_24-08-2025-12-00_jCmuZwaX8DClwrXDKhYVyL1rSD1f62pl9wUURec4_products.csv'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        rows = list(reader)
    
    print(f"📊 Total rows analyzed: {len(rows)}")
    
    # Separate main products and variants
    main_products = [row for row in rows if row.get('MPN', '').strip() == 'منتج']
    variants = [row for row in rows if row.get('MPN', '').strip() == 'خيار']
    
    print(f"📦 Main products: {len(main_products)}")
    print(f"🎨 Variants: {len(variants)}")
    
    # MAIN PRODUCT IMAGE ANALYSIS
    print(f"\n📸 MAIN PRODUCT IMAGE ANALYSIS")
    print("-" * 40)
    
    main_with_images = 0
    main_without_images = 0
    main_image_details = []
    all_main_images = []
    
    for i, product in enumerate(main_products, 1):
        product_name = product.get('سعر التخفيض', '').strip()
        images_field = product.get('رمز المنتج sku', '').strip()
        price = product.get('هل يتطلب شحن؟', '').strip()
        category = product.get('سعر التكلفة', '').strip()
        
        if images_field and 'http' in images_field:
            main_with_images += 1
            # Split comma-separated images
            image_list = [img.strip() for img in images_field.split(',') if img.strip() and 'http' in img.strip()]
            all_main_images.extend(image_list)
            
            main_image_details.append({
                'index': i,
                'name': product_name,
                'category': category,
                'price': price,
                'image_count': len(image_list),
                'images': image_list,
                'status': 'HAS_IMAGES'
            })
            
            print(f"{i:3d}. ✅ {product_name[:35]:<35} | {len(image_list):2d} images | {price} SAR")
            
        else:
            main_without_images += 1
            main_image_details.append({
                'index': i,
                'name': product_name,
                'category': category,
                'price': price,
                'image_count': 0,
                'images': [],
                'status': 'NO_IMAGES'
            })
            
            print(f"{i:3d}. ❌ {product_name[:35]:<35} | No images    | {price} SAR")
    
    # VARIANT IMAGE ANALYSIS
    print(f"\n🎨 VARIANT IMAGE ANALYSIS")
    print("-" * 40)
    
    variant_with_images = 0
    variant_without_images = 0
    variant_image_details = []
    all_variant_images = []
    color_mapping = defaultdict(dict)
    
    for i, variant in enumerate(variants, 1):
        product_name = variant.get('سعر التخفيض', '').strip()
        color = variant.get('[2] الصورة / اللون', '').strip()
        image_url = variant.get('[2] القيمة', '').strip()
        price = variant.get('هل يتطلب شحن؟', '').strip()
        
        if image_url and 'http' in image_url and ('.jpg' in image_url or '.png' in image_url):
            variant_with_images += 1
            all_variant_images.append(image_url)
            color_mapping[product_name][color] = image_url
            
            variant_image_details.append({
                'index': i,
                'product': product_name,
                'color': color,
                'price': price,
                'image_url': image_url,
                'status': 'HAS_IMAGE'
            })
            
            if i <= 30:  # Show first 30 variants
                print(f"{i:3d}. ✅ {product_name[:20]:<20} | {color:<8} | {price} SAR")
                
        else:
            variant_without_images += 1
            variant_image_details.append({
                'index': i,
                'product': product_name,
                'color': color,
                'price': price,
                'image_url': '',
                'status': 'NO_IMAGE'
            })
            
            if i <= 10:  # Show first 10 missing
                print(f"{i:3d}. ❌ {product_name[:20]:<20} | {color:<8} | No image")
    
    # COMPREHENSIVE IMAGE SUMMARY
    print(f"\n📊 COMPREHENSIVE IMAGE SUMMARY")
    print("=" * 50)
    
    total_images = len(all_main_images) + len(all_variant_images)
    unique_images = len(set(all_main_images + all_variant_images))
    
    print(f"📸 MAIN PRODUCTS:")
    print(f"   ✅ WITH images: {main_with_images:3d}/{len(main_products)} ({(main_with_images/len(main_products)*100):5.1f}%)")
    print(f"   ❌ WITHOUT images: {main_without_images:3d}/{len(main_products)} ({(main_without_images/len(main_products)*100):5.1f}%)")
    print(f"   📷 Total main images: {len(all_main_images)}")
    
    print(f"\n🎨 VARIANTS:")
    print(f"   ✅ WITH images: {variant_with_images:4d}/{len(variants)} ({(variant_with_images/len(variants)*100):5.1f}%)")
    print(f"   ❌ WITHOUT images: {variant_without_images:4d}/{len(variants)} ({(variant_without_images/len(variants)*100):5.1f}%)")
    print(f"   📷 Total variant images: {len(all_variant_images)}")
    
    print(f"\n🎯 OVERALL TOTALS:")
    print(f"   📷 Total images: {total_images}")
    print(f"   🔗 Unique images: {unique_images}")
    print(f"   📊 Image efficiency: {(unique_images/total_images*100):5.1f}%")
    
    # PRODUCTS BY CATEGORY WITH IMAGE STATUS
    print(f"\n📂 PRODUCTS BY CATEGORY (with image status)")
    print("-" * 50)
    
    category_stats = defaultdict(lambda: {'with_images': 0, 'without_images': 0, 'total': 0})
    
    for detail in main_image_details:
        category = detail['category'] if detail['category'] else 'No Category'
        category_stats[category]['total'] += 1
        if detail['status'] == 'HAS_IMAGES':
            category_stats[category]['with_images'] += 1
        else:
            category_stats[category]['without_images'] += 1
    
    for category, stats in category_stats.items():
        success_rate = (stats['with_images'] / stats['total'] * 100) if stats['total'] > 0 else 0
        print(f"{category[:30]:<30} | {stats['with_images']:2d}✅ {stats['without_images']:2d}❌ | {success_rate:5.1f}%")
    
    # SAVE DETAILED ANALYSIS TO FILE
    analysis_data = {
        'summary': {
            'total_rows': len(rows),
            'main_products': len(main_products),
            'variants': len(variants),
            'main_with_images': main_with_images,
            'main_without_images': main_without_images,
            'variant_with_images': variant_with_images,
            'variant_without_images': variant_without_images,
            'total_images': total_images,
            'unique_images': unique_images
        },
        'main_products': main_image_details,
        'variants': variant_image_details[:100],  # First 100 variants
        'color_mapping': dict(color_mapping)
    }
    
    with open('local_image_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 DETAILED REPORT SAVED:")
    print(f"   📄 local_image_analysis_report.json")
    print(f"   📊 Contains complete image mapping and analysis")
    
    print(f"\n🎯 READY FOR SHOPIFY CONVERSION:")
    print(f"   ✅ {main_with_images} products will have images immediately")
    print(f"   ⚠️  {main_without_images} products will need manual images")
    print(f"   📸 {unique_images} unique images ready for import")
    print(f"   🚀 Excellent image coverage for immediate store launch!")

if __name__ == "__main__":
    analyze_images_locally()
