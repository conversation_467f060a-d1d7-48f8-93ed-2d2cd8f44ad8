#!/usr/bin/env python3
"""
Fix CSV column issues - remove invalid image sources and fix structure
"""

import csv

def fix_csv_columns(input_file, output_file):
    """Fix column issues in the Shopify CSV"""
    print(f"Fixing column issues in {input_file}...")
    
    fixed_rows = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        fieldnames = reader.fieldnames
        
        for row in reader:
            # Fix image source issues
            image_src = row.get('Image Src', '').strip()
            variant_image = row.get('Variant Image', '').strip()
            
            # If Image Src doesn't start with http, it's probably a SKU - remove it
            if image_src and not image_src.startswith('http'):
                row['Image Src'] = ''
                row['Image Position'] = ''
            
            # If Variant Image doesn't start with http, it's probably a SKU - remove it
            if variant_image and not variant_image.startswith('http'):
                row['Variant Image'] = ''
            
            # If this is an image-only row with no valid image, skip it
            title = row.get('Title', '').strip()
            variant_sku = row.get('Variant SKU', '').strip()
            
            if not title and not variant_sku and not row.get('Image Src'):
                continue  # Skip empty rows
            
            fixed_rows.append(row)
    
    # Write fixed CSV
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(fixed_rows)
    
    print(f"✅ Fixed CSV: {len(fixed_rows)} rows")
    print(f"📁 Output: {output_file}")

if __name__ == "__main__":
    fix_csv_columns("shopify-import-perfect.csv", "shopify-import-fixed-final.csv")
