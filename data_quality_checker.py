#!/usr/bin/env python3
"""
Data Quality Assurance for Shopify Import
Validates converted CSV data for completeness and correctness
"""

import csv
import re
from collections import defaultdict, Counter
import json

def check_required_fields(row):
    """Check if required Shopify fields are present"""
    required_fields = ['Handle', 'Title', 'Vendor', 'Product Type', 'Published']
    missing_fields = []
    
    for field in required_fields:
        if not row.get(field, '').strip():
            missing_fields.append(field)
    
    return missing_fields

def validate_handle(handle):
    """Validate Shopify handle format"""
    if not handle:
        return False, "Handle is empty"
    
    # Shopify handle rules: lowercase, alphanumeric, hyphens, no spaces
    if not re.match(r'^[a-z0-9-]+$', handle):
        return False, "Handle contains invalid characters"
    
    if len(handle) > 255:
        return False, "Handle too long (max 255 characters)"
    
    if handle.startswith('-') or handle.endswith('-'):
        return False, "Handle cannot start or end with hyphen"
    
    return True, "Valid"

def validate_price(price):
    """Validate price format"""
    if not price:
        return False, "Price is empty"
    
    try:
        price_float = float(price)
        if price_float < 0:
            return False, "Price cannot be negative"
        if price_float > 999999:
            return False, "Price too high"
        return True, "Valid"
    except ValueError:
        return False, "Invalid price format"

def validate_inventory(qty):
    """Validate inventory quantity"""
    if not qty:
        return False, "Inventory quantity is empty"
    
    try:
        qty_int = int(qty)
        if qty_int < 0:
            return False, "Inventory cannot be negative"
        return True, "Valid"
    except ValueError:
        return False, "Invalid inventory format"

def analyze_product_structure(csv_file):
    """Analyze product-variant relationships"""
    products = defaultdict(list)
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for i, row in enumerate(reader, 1):
            handle = row.get('Handle', '').strip()
            if handle:
                products[handle].append({
                    'row_number': i,
                    'has_title': bool(row.get('Title', '').strip()),
                    'has_body': bool(row.get('Body (HTML)', '').strip()),
                    'has_variant_sku': bool(row.get('Variant SKU', '').strip()),
                    'has_variant_price': bool(row.get('Variant Price', '').strip()),
                    'has_option_value': bool(row.get('Option1 Value', '').strip()),
                    'has_image': bool(row.get('Image Src', '').strip()),
                    'has_variant_image': bool(row.get('Variant Image', '').strip()),
                    'row': row
                })
    
    return products

def validate_csv_data(csv_file):
    """Main validation function"""
    print(f"Validating data quality in {csv_file}...")
    
    issues = []
    stats = {
        'total_rows': 0,
        'product_rows': 0,
        'variant_rows': 0,
        'image_rows': 0,
        'unique_handles': 0,
        'missing_required_fields': 0,
        'invalid_handles': 0,
        'invalid_prices': 0,
        'invalid_inventory': 0,
        'missing_images': 0
    }
    
    # Analyze product structure
    products = analyze_product_structure(csv_file)
    stats['unique_handles'] = len(products)
    
    # Validate each product group
    for handle, rows in products.items():
        # Check handle validity
        is_valid_handle, handle_msg = validate_handle(handle)
        if not is_valid_handle:
            issues.append({
                'type': 'invalid_handle',
                'handle': handle,
                'message': handle_msg
            })
            stats['invalid_handles'] += 1
        
        # Find main product row (first row with title and body)
        main_product_row = None
        for row_data in rows:
            if row_data['has_title'] and row_data['has_body']:
                main_product_row = row_data
                break
        
        if not main_product_row:
            issues.append({
                'type': 'missing_main_product',
                'handle': handle,
                'message': 'No main product row found (missing title or body)'
            })
        
        # Validate each row
        for row_data in rows:
            stats['total_rows'] += 1
            row = row_data['row']
            
            # Classify row type
            if row_data['has_title'] and row_data['has_body']:
                stats['product_rows'] += 1
            elif row_data['has_variant_sku'] and row_data['has_option_value']:
                stats['variant_rows'] += 1
            elif row_data['has_image']:
                stats['image_rows'] += 1
            
            # Check required fields
            missing_fields = check_required_fields(row)
            if missing_fields:
                stats['missing_required_fields'] += 1
                issues.append({
                    'type': 'missing_required_fields',
                    'handle': handle,
                    'row': row_data['row_number'],
                    'missing_fields': missing_fields
                })
            
            # Validate price
            price = row.get('Variant Price', '').strip()
            if price:
                is_valid_price, price_msg = validate_price(price)
                if not is_valid_price:
                    stats['invalid_prices'] += 1
                    issues.append({
                        'type': 'invalid_price',
                        'handle': handle,
                        'row': row_data['row_number'],
                        'price': price,
                        'message': price_msg
                    })
            
            # Validate inventory
            inventory = row.get('Variant Inventory Qty', '').strip()
            if inventory:
                is_valid_inventory, inventory_msg = validate_inventory(inventory)
                if not is_valid_inventory:
                    stats['invalid_inventory'] += 1
                    issues.append({
                        'type': 'invalid_inventory',
                        'handle': handle,
                        'row': row_data['row_number'],
                        'inventory': inventory,
                        'message': inventory_msg
                    })
            
            # Check for missing images in variant rows
            if row_data['has_variant_sku'] and not row_data['has_variant_image']:
                stats['missing_images'] += 1
                issues.append({
                    'type': 'missing_variant_image',
                    'handle': handle,
                    'row': row_data['row_number'],
                    'sku': row.get('Variant SKU', '')
                })
    
    return stats, issues, products

def generate_quality_report(stats, issues, products, output_file):
    """Generate comprehensive quality report"""
    
    # Calculate quality scores
    total_rows = stats['total_rows']
    quality_score = 100
    
    if total_rows > 0:
        quality_score -= (stats['missing_required_fields'] / total_rows) * 30
        quality_score -= (stats['invalid_handles'] / stats['unique_handles']) * 20
        quality_score -= (stats['invalid_prices'] / total_rows) * 15
        quality_score -= (stats['missing_images'] / total_rows) * 10
        quality_score = max(0, quality_score)
    
    # Group issues by type
    issues_by_type = defaultdict(list)
    for issue in issues:
        issues_by_type[issue['type']].append(issue)
    
    # Product structure analysis
    product_analysis = {}
    for handle, rows in products.items():
        variant_count = sum(1 for r in rows if r['has_variant_sku'])
        image_count = sum(1 for r in rows if r['has_image'])
        
        product_analysis[handle] = {
            'total_rows': len(rows),
            'variant_count': variant_count,
            'image_count': image_count,
            'has_main_product': any(r['has_title'] and r['has_body'] for r in rows)
        }
    
    report = {
        'summary': {
            'quality_score': f"{quality_score:.1f}%",
            'total_issues': len(issues),
            'critical_issues': len([i for i in issues if i['type'] in ['missing_main_product', 'invalid_handle']]),
            'data_completeness': f"{((total_rows - stats['missing_required_fields']) / total_rows * 100):.1f}%" if total_rows > 0 else "0%"
        },
        'statistics': stats,
        'issues_by_type': {k: len(v) for k, v in issues_by_type.items()},
        'sample_issues': {k: v[:5] for k, v in issues_by_type.items()},  # First 5 of each type
        'product_analysis': {
            'products_with_variants': len([p for p in product_analysis.values() if p['variant_count'] > 0]),
            'products_with_images': len([p for p in product_analysis.values() if p['image_count'] > 0]),
            'average_variants_per_product': sum(p['variant_count'] for p in product_analysis.values()) / len(product_analysis) if product_analysis else 0,
            'average_images_per_product': sum(p['image_count'] for p in product_analysis.values()) / len(product_analysis) if product_analysis else 0
        }
    }
    
    # Save report
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    return report

def print_quality_summary(report):
    """Print quality assurance summary"""
    print("\n" + "="*60)
    print("DATA QUALITY ASSURANCE SUMMARY")
    print("="*60)
    
    summary = report['summary']
    stats = report['statistics']
    
    print(f"Quality Score: {summary['quality_score']}")
    print(f"Data Completeness: {summary['data_completeness']}")
    print(f"Total Issues: {summary['total_issues']}")
    print(f"Critical Issues: {summary['critical_issues']}")
    
    print(f"\nData Statistics:")
    print(f"  Total Rows: {stats['total_rows']}")
    print(f"  Unique Products: {stats['unique_handles']}")
    print(f"  Product Rows: {stats['product_rows']}")
    print(f"  Variant Rows: {stats['variant_rows']}")
    print(f"  Image Rows: {stats['image_rows']}")
    
    if report['issues_by_type']:
        print(f"\nIssues by Type:")
        for issue_type, count in report['issues_by_type'].items():
            print(f"  {issue_type}: {count}")
    
    print(f"\nProduct Analysis:")
    pa = report['product_analysis']
    print(f"  Products with variants: {pa['products_with_variants']}")
    print(f"  Products with images: {pa['products_with_images']}")
    print(f"  Avg variants per product: {pa['average_variants_per_product']:.1f}")
    print(f"  Avg images per product: {pa['average_images_per_product']:.1f}")
    
    print("\n" + "="*60)

def main():
    csv_file = "shopify-import.csv"
    report_file = "data_quality_report.json"
    
    try:
        # Validate data
        stats, issues, products = validate_csv_data(csv_file)
        
        # Generate report
        print("\nGenerating quality assurance report...")
        report = generate_quality_report(stats, issues, products, report_file)
        
        # Print summary
        print_quality_summary(report)
        
        print(f"\n✅ Data quality check completed!")
        print(f"📊 Detailed report saved to: {report_file}")
        
        # Recommendations
        quality_score = float(report['summary']['quality_score'].rstrip('%'))
        if quality_score >= 95:
            print("🎉 Excellent! Data quality is very high.")
        elif quality_score >= 85:
            print("✅ Good! Data quality is acceptable.")
        elif quality_score >= 70:
            print("⚠️  Warning: Some data quality issues need attention.")
        else:
            print("❌ Alert: Significant data quality issues found.")
        
    except Exception as e:
        print(f"❌ Error during quality check: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
