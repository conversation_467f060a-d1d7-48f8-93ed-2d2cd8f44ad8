#!/usr/bin/env python3
"""
Fix Shopify CSV structure to properly group products with variants
"""

import csv
from collections import defaultdict

def fix_shopify_structure(input_file, output_file):
    """Fix the CSV structure for proper Shopify import"""
    print(f"Fixing Shopify structure in {input_file}...")
    
    # Read all rows and group by handle
    products = defaultdict(list)
    
    with open(input_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        fieldnames = reader.fieldnames
        
        for row in reader:
            handle = row.get('Handle', '').strip()
            if handle:
                products[handle].append(row)
    
    print(f"Found {len(products)} unique product handles")
    
    # Create properly structured rows
    fixed_rows = []
    
    for handle, rows in products.items():
        # Find the main product row (has title and description)
        main_product_row = None
        variant_rows = []
        image_rows = []
        
        for row in rows:
            title = row.get('Title', '').strip()
            variant_sku = row.get('Variant SKU', '').strip()
            image_src = row.get('Image Src', '').strip()
            option_value = row.get('Option1 Value', '').strip()
            
            if title and row.get('Body (HTML)', '').strip():
                # This is a main product row
                if not main_product_row:
                    main_product_row = row
            elif variant_sku and option_value:
                # This is a variant row
                variant_rows.append(row)
            elif image_src and not variant_sku:
                # This is an image-only row
                image_rows.append(row)
        
        if not main_product_row:
            print(f"Warning: No main product row found for handle {handle}")
            continue
        
        # Create the main product row (includes first variant if available)
        if variant_rows:
            # Use first variant data in main product row
            first_variant = variant_rows[0]
            main_product_row['Option1 Value'] = first_variant.get('Option1 Value', '')
            main_product_row['Variant SKU'] = first_variant.get('Variant SKU', '')
            main_product_row['Variant Price'] = first_variant.get('Variant Price', '219.00')
            main_product_row['Variant Image'] = first_variant.get('Variant Image', '')
            
            # Remove first variant from variant_rows since it's now in main row
            variant_rows = variant_rows[1:]
        
        # Add main product row
        fixed_rows.append(main_product_row)
        
        # Add additional image rows (without variant data)
        for i, image_row in enumerate(image_rows, 2):  # Start from position 2
            image_only_row = {key: '' for key in fieldnames}
            image_only_row.update({
                'Handle': handle,
                'Image Src': image_row.get('Image Src', ''),
                'Image Position': str(i),
                'Image Alt Text': main_product_row.get('Title', '')
            })
            fixed_rows.append(image_only_row)
        
        # Add additional variant rows (without product data)
        for variant_row in variant_rows:
            variant_only_row = {key: '' for key in fieldnames}
            variant_only_row.update({
                'Handle': handle,
                'Option1 Name': 'Color',
                'Option1 Value': variant_row.get('Option1 Value', ''),
                'Variant SKU': variant_row.get('Variant SKU', ''),
                'Variant Grams': '500',
                'Variant Inventory Tracker': 'shopify',
                'Variant Inventory Qty': '10',
                'Variant Inventory Policy': 'deny',
                'Variant Fulfillment Service': 'manual',
                'Variant Price': variant_row.get('Variant Price', '219.00'),
                'Variant Compare At Price': '',
                'Variant Requires Shipping': 'TRUE',
                'Variant Taxable': 'TRUE',
                'Variant Barcode': '',
                'Variant Image': variant_row.get('Variant Image', ''),
                'Variant Weight Unit': 'g'
            })
            fixed_rows.append(variant_only_row)
    
    # Write fixed CSV
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(fixed_rows)
    
    print(f"\n✅ Fixed structure: {len(fixed_rows)} total rows for {len(products)} products")
    print(f"📁 Fixed file saved as: {output_file}")
    
    # Calculate expected results
    total_variants = sum(1 for row in fixed_rows if row.get('Variant SKU'))
    total_images = sum(1 for row in fixed_rows if row.get('Image Src') and not row.get('Variant SKU'))
    
    print(f"📊 Expected import: {len(products)} products, {total_variants} variants, {total_images} additional images")

if __name__ == "__main__":
    fix_shopify_structure("shopify-import-final.csv", "shopify-import-corrected.csv")
