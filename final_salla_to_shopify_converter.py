#!/usr/bin/env python3
"""
Final Salla to Shopify converter with unique handles for each product
"""

import csv
import re
import hashlib
from collections import defaultdict

def create_unique_handle(title, product_id):
    """Create a unique Shopify handle from title and product ID"""
    # Create base handle from specific product names
    title_lower = title.lower()
    
    if 'حروف بني' in title:
        base_handle = 'brown-letters'
    elif 'حروف عنابي' in title:
        base_handle = 'burgundy-letters'
    elif 'حروف أخضر' in title:
        base_handle = 'green-letters'
    elif 'حروف' in title:
        base_handle = 'letters'
    elif 'سيفين ونخله' in title:
        base_handle = 'swords-palm'
    elif 'حقيبة سهرة' in title:
        base_handle = 'evening-bag'
    elif 'حقيبة كتف' in title:
        base_handle = 'shoulder-bag'
    elif 'حقيبة يد' in title:
        base_handle = 'handbag'
    elif 'حقيبة كروس' in title or 'كروس بودي' in title:
        base_handle = 'crossbody-bag'
    elif 'حقيبة باوتش' in title:
        base_handle = 'pouch-bag'
    elif 'حقيبة توتي' in title:
        base_handle = 'tote-bag'
    else:
        # Create handle from title hash for uniqueness
        title_hash = hashlib.md5(title.encode('utf-8')).hexdigest()[:8]
        base_handle = f'product-{title_hash}'
    
    # Add product ID for absolute uniqueness
    handle = f"{base_handle}-{product_id}"
    
    # Ensure ASCII only and proper format
    handle = re.sub(r'[^a-z0-9-]', '', handle.lower())
    handle = re.sub(r'-+', '-', handle)  # Remove multiple hyphens
    handle = handle.strip('-')
    
    return handle[:100]  # Shopify handle limit

def convert_salla_to_shopify(input_file, output_file):
    """Convert Salla CSV to Shopify format with proper structure"""
    print(f"Converting {input_file} to Shopify format...")
    
    # Shopify CSV headers
    shopify_headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Type', 'Tags',
        'Published', 'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value',
        'Option3 Name', 'Option3 Value', 'Variant SKU', 'Variant Grams',
        'Variant Inventory Tracker', 'Variant Inventory Qty', 'Variant Inventory Policy',
        'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode',
        'Image Src', 'Image Position', 'Image Alt Text', 'Gift Card',
        'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / MPN', 'Google Shopping / Age Group', 'Google Shopping / Gender',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0',
        'Google Shopping / Custom Label 1', 'Google Shopping / Custom Label 2',
        'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]
    
    products_processed = 0
    rows_created = 0
    
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8', newline='') as outfile:
        
        reader = csv.DictReader(infile)
        writer = csv.DictWriter(outfile, fieldnames=shopify_headers)
        writer.writeheader()
        
        # Group products by name to handle variants
        products = defaultdict(list)

        for row in reader:
            product_name = row.get('اسم المنتج', '').strip()
            if not product_name:
                # Try alternative column names
                product_name = row.get('سعر التخفيض', '').strip()  # This seems to contain the product name

            if product_name:
                products[product_name].append(row)

        print(f"Found {len(products)} unique products")

        for product_name, product_rows in products.items():
            # Get main product info from first row
            main_row = product_rows[0]

            title = product_name
            if not title:
                continue

            # Use GTIN as product ID for handle creation
            product_id = main_row.get('GTIN', '').strip() or main_row.get('رقم المنتج', '').strip()
            if not product_id:
                product_id = str(hash(title))[:8]

            handle = create_unique_handle(title, product_id)
            description = main_row.get('الوصف', '').strip()

            # Clean HTML description
            if description:
                description = f"<p>{description}</p>"
            else:
                description = f"<p>{title}</p>"

            # Determine price based on product type
            if 'حروف' in title:
                price = '39.00'
            else:
                price = '219.00'

            # Get all variants for this product
            variants = []
            images = []

            for row in product_rows:
                # Get variant info from [2] columns (seems to be the main variant data)
                variant_name = row.get('[2] القيمة', '').strip()
                variant_sku = row.get('رمز المنتج sku', '').strip()
                variant_image = row.get('[2] الصورة / اللون', '').strip()

                if variant_name and variant_sku:
                    variants.append({
                        'name': variant_name,
                        'sku': variant_sku,
                        'image': variant_image,
                        'price': price
                    })

                # Collect images
                main_image = row.get('صورة المنتج', '').strip()
                if main_image and main_image not in images:
                    images.append(main_image)

                # Check variant images
                for col in ['[1] الصورة / اللون', '[2] الصورة / اللون', '[3] الصورة / اللون']:
                    img_url = row.get(col, '').strip()
                    if img_url and img_url not in images and img_url.startswith('http'):
                        images.append(img_url)

            # If no variants found, create a default variant
            if not variants:
                default_sku = main_row.get('رمز المنتج sku', '').strip() or product_id
                variants.append({
                    'name': 'Default Title',
                    'sku': default_sku,
                    'image': images[0] if images else '',
                    'price': price
                })
            
            # Create main product row with first variant
            first_variant = variants[0]
            main_shopify_row = {
                'Handle': handle,
                'Title': title,
                'Body (HTML)': description,
                'Vendor': 'ChrisBella',
                'Product Type': 'حقائب' if 'حقيبة' in title else 'حروف',
                'Tags': f"{title}, حقائب" if 'حقيبة' in title else f"{title}, حروف",
                'Published': 'TRUE',
                'Option1 Name': 'Color' if len(variants) > 1 else '',
                'Option1 Value': first_variant['name'] if len(variants) > 1 else '',
                'Variant SKU': first_variant['sku'],
                'Variant Grams': '500',
                'Variant Inventory Tracker': 'shopify',
                'Variant Inventory Qty': '10',
                'Variant Inventory Policy': 'deny',
                'Variant Fulfillment Service': 'manual',
                'Variant Price': first_variant['price'],
                'Variant Requires Shipping': 'TRUE',
                'Variant Taxable': 'TRUE',
                'Image Src': images[0] if images else '',
                'Image Position': '1',
                'Image Alt Text': title,
                'Gift Card': 'FALSE',
                'SEO Title': title,
                'SEO Description': title,
                'Google Shopping / Age Group': 'adult',
                'Google Shopping / Gender': 'female',
                'Google Shopping / Custom Product': 'FALSE',
                'Variant Image': first_variant['image'],
                'Variant Weight Unit': 'g',
                'Status': 'active'
            }
            
            writer.writerow(main_shopify_row)
            rows_created += 1
            
            # Add additional images
            for i, image_url in enumerate(images[1:], 2):
                image_row = {key: '' for key in shopify_headers}
                image_row.update({
                    'Handle': handle,
                    'Image Src': image_url,
                    'Image Position': str(i),
                    'Image Alt Text': title,
                    'Published': 'TRUE',
                    'Gift Card': 'FALSE'
                })
                writer.writerow(image_row)
                rows_created += 1
            
            # Add additional variants
            for variant in variants[1:]:
                variant_row = {key: '' for key in shopify_headers}
                variant_row.update({
                    'Handle': handle,
                    'Option1 Name': 'Color',
                    'Option1 Value': variant['name'],
                    'Variant SKU': variant['sku'],
                    'Variant Grams': '500',
                    'Variant Inventory Tracker': 'shopify',
                    'Variant Inventory Qty': '10',
                    'Variant Inventory Policy': 'deny',
                    'Variant Fulfillment Service': 'manual',
                    'Variant Price': variant['price'],
                    'Variant Requires Shipping': 'TRUE',
                    'Variant Taxable': 'TRUE',
                    'Variant Image': variant['image'],
                    'Variant Weight Unit': 'g',
                    'Published': 'TRUE',
                    'Gift Card': 'FALSE',
                    'Status': 'active'
                })
                writer.writerow(variant_row)
                rows_created += 1
            
            products_processed += 1
            
            if products_processed % 50 == 0:
                print(f"Processed {products_processed} products...")
    
    print(f"\n✅ Conversion complete!")
    print(f"📊 Products processed: {products_processed}")
    print(f"📊 Total rows created: {rows_created}")
    print(f"📁 Output file: {output_file}")

if __name__ == "__main__":
    convert_salla_to_shopify(
        "chrisbella_24-08-2025-12-00_jCmuZwaX8DClwrXDKhYVyL1rSD1f62pl9wUURec4_products.csv",
        "shopify-import-perfect.csv"
    )
